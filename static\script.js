// Pandoc Web UI - JavaScript Interactions

class PandocWebUI {
    constructor() {
        this.currentFile = null;
        this.currentFileId = null;
        this.supportedFormats = {
            input: [],
            output: []
        };
        
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadAppInfo();
        await this.loadHistory();
        this.setupDragAndDrop();
    }

    setupEventListeners() {
        // File input
        const fileInput = document.getElementById('file-input');
        const uploadArea = document.getElementById('upload-area');
        const removeFileBtn = document.getElementById('remove-file');
        
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        uploadArea.addEventListener('click', () => fileInput.click());
        removeFileBtn.addEventListener('click', () => this.removeFile());

        // Format selection
        const inputFormat = document.getElementById('input-format');
        const outputFormat = document.getElementById('output-format');
        
        inputFormat.addEventListener('change', () => this.updateConvertButton());
        outputFormat.addEventListener('change', () => this.updateConvertButton());

        // Advanced options
        const toggleAdvanced = document.getElementById('toggle-advanced');
        toggleAdvanced.addEventListener('click', () => this.toggleAdvancedOptions());

        // Convert button
        const convertBtn = document.getElementById('convert-btn');
        convertBtn.addEventListener('click', () => this.convertFile());

        // History actions
        const clearHistoryBtn = document.getElementById('clear-history');
        clearHistoryBtn.addEventListener('click', () => this.clearHistory());
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('upload-area');
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => uploadArea.classList.add('dragover'), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => uploadArea.classList.remove('dragover'), false);
        });

        uploadArea.addEventListener('drop', (e) => this.handleDrop(e), false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    handleDrop(e) {
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFileSelect({ target: { files } });
        }
    }

    async loadAppInfo() {
        try {
            const response = await fetch('/api/info');
            const info = await response.json();
            
            // Update Pandoc version
            document.getElementById('pandoc-version').textContent = info.pandoc_version;
            
            // Store supported formats
            this.supportedFormats.input = info.supported_input_formats;
            this.supportedFormats.output = info.supported_output_formats;
            
            // Populate format selectors
            this.populateFormatSelectors();
            
        } catch (error) {
            this.showToast('Failed to load application info', 'error');
        }
    }

    populateFormatSelectors() {
        const inputSelect = document.getElementById('input-format');
        const outputSelect = document.getElementById('output-format');
        
        // Clear existing options (except auto for input)
        inputSelect.innerHTML = '<option value="auto">自动检测</option>';
        outputSelect.innerHTML = '';
        
        // Add input formats
        this.supportedFormats.input.forEach(format => {
            const option = document.createElement('option');
            option.value = format;
            option.textContent = this.formatDisplayName(format);
            inputSelect.appendChild(option);
        });
        
        // Add output formats
        this.supportedFormats.output.forEach(format => {
            const option = document.createElement('option');
            option.value = format;
            option.textContent = this.formatDisplayName(format);
            outputSelect.appendChild(option);
        });
        
        // Set default output format
        outputSelect.value = 'html';
    }

    formatDisplayName(format) {
        const displayNames = {
            // Input formats
            'bibtex': 'BibTeX Bibliography (.bib)',
            'biblatex': 'BibLaTeX Bibliography (.bib)',
            'bits': 'BITS XML (.xml)',
            'commonmark': 'CommonMark (.md)',
            'commonmark_x': 'CommonMark Extended (.md)',
            'creole': 'Creole 1.0 (.creole)',
            'csljson': 'CSL JSON Bibliography (.json)',
            'csv': 'CSV Table (.csv)',
            'tsv': 'TSV Table (.tsv)',
            'djot': 'Djot Markup (.dj)',
            'docbook': 'DocBook (.xml)',
            'docx': 'Word Document (.docx)',
            'dokuwiki': 'DokuWiki (.txt)',
            'endnotexml': 'EndNote XML (.xml)',
            'epub': 'EPUB (.epub)',
            'fb2': 'FictionBook2 (.fb2)',
            'gfm': 'GitHub Flavored Markdown (.md)',
            'haddock': 'Haddock Markup (.txt)',
            'html': 'HTML (.html)',
            'ipynb': 'Jupyter Notebook (.ipynb)',
            'jats': 'JATS XML (.xml)',
            'jira': 'Jira/Confluence (.txt)',
            'json': 'JSON (.json)',
            'latex': 'LaTeX (.tex)',
            'markdown': 'Pandoc Markdown (.md)',
            'markdown_mmd': 'MultiMarkdown (.md)',
            'markdown_phpextra': 'PHP Markdown Extra (.md)',
            'markdown_strict': 'Strict Markdown (.md)',
            'mediawiki': 'MediaWiki (.wiki)',
            'man': 'Roff Man (.man)',
            'mdoc': 'Mdoc Manual (.mdoc)',
            'muse': 'Muse (.muse)',
            'native': 'Native Haskell (.hs)',
            'odt': 'OpenDocument Text (.odt)',
            'opml': 'OPML (.opml)',
            'org': 'Emacs Org Mode (.org)',
            'pod': 'Perl POD (.pod)',
            'ris': 'RIS Bibliography (.ris)',
            'rtf': 'Rich Text Format (.rtf)',
            'rst': 'reStructuredText (.rst)',
            't2t': 'txt2tags (.t2t)',
            'textile': 'Textile (.textile)',
            'tikiwiki': 'TikiWiki (.txt)',
            'twiki': 'TWiki (.txt)',
            'typst': 'Typst (.typ)',
            'vimwiki': 'Vimwiki (.txt)',

            // Output formats
            'ansi': 'ANSI Text (.txt)',
            'asciidoc': 'AsciiDoc (.adoc)',
            'asciidoc_legacy': 'AsciiDoc Legacy (.adoc)',
            'asciidoctor': 'AsciiDoctor (.adoc)',
            'beamer': 'LaTeX Beamer (.tex)',
            'chunkedhtml': 'Chunked HTML (.zip)',
            'context': 'ConTeXt (.tex)',
            'docbook4': 'DocBook 4 (.xml)',
            'docbook5': 'DocBook 5 (.xml)',
            'epub2': 'EPUB v2 (.epub)',
            'epub3': 'EPUB v3 (.epub)',
            'html4': 'XHTML 1.0 (.html)',
            'html5': 'HTML5 (.html)',
            'icml': 'InDesign ICML (.icml)',
            'jats_archiving': 'JATS Archiving (.xml)',
            'jats_articleauthoring': 'JATS Article Authoring (.xml)',
            'jats_publishing': 'JATS Publishing (.xml)',
            'markua': 'Markua (.md)',
            'ms': 'Roff Ms (.ms)',
            'opendocument': 'OpenDocument XML (.xml)',
            'pdf': 'PDF (.pdf)',
            'plain': 'Plain Text (.txt)',
            'pptx': 'PowerPoint (.pptx)',
            'texinfo': 'GNU Texinfo (.texi)',
            'slideous': 'Slideous Slides (.html)',
            'slidy': 'Slidy Slides (.html)',
            'dzslides': 'DZSlides (.html)',
            'revealjs': 'Reveal.js Slides (.html)',
            's5': 'S5 Slides (.html)',
            'tei': 'TEI Simple (.xml)',
            'xwiki': 'XWiki (.txt)',
            'zimwiki': 'ZimWiki (.txt)'
        };

        return displayNames[format] || format.toUpperCase();
    }

    handleFileSelect(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        // Validate file size (50MB limit)
        if (file.size > 50 * 1024 * 1024) {
            this.showToast('文件大小不能超过 50MB', 'error');
            return;
        }
        
        this.currentFile = file;
        this.showFileInfo(file);
        this.updateConvertButton();
        
        // Auto-detect input format based on file extension
        this.autoDetectFormat(file.name);
    }

    autoDetectFormat(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        const formatMap = {
            // Markdown variants
            'md': 'markdown',
            'markdown': 'markdown',
            'mdown': 'markdown',
            'mkd': 'markdown',
            'mkdn': 'markdown',

            // HTML
            'html': 'html',
            'htm': 'html',
            'xhtml': 'html',

            // Office documents
            'docx': 'docx',
            'odt': 'odt',
            'rtf': 'rtf',

            // E-books
            'epub': 'epub',
            'fb2': 'fb2',

            // LaTeX
            'tex': 'latex',
            'latex': 'latex',

            // Other markup
            'rst': 'rst',
            'textile': 'textile',
            'org': 'org',
            'wiki': 'mediawiki',
            'creole': 'creole',
            't2t': 't2t',
            'typ': 'typst',

            // Data formats
            'csv': 'csv',
            'tsv': 'tsv',
            'json': 'json',
            'bib': 'bibtex',
            'ris': 'ris',

            // Notebooks
            'ipynb': 'ipynb',

            // Documentation
            'pod': 'pod',
            'man': 'man',

            // Default fallback
            'txt': 'markdown'
        };

        const detectedFormat = formatMap[extension];
        if (detectedFormat && this.supportedFormats.input.includes(detectedFormat)) {
            document.getElementById('input-format').value = detectedFormat;
        }
    }

    showFileInfo(file) {
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');
        
        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        fileInfo.style.display = 'block';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    removeFile() {
        this.currentFile = null;
        this.currentFileId = null;
        document.getElementById('file-info').style.display = 'none';
        document.getElementById('file-input').value = '';
        this.updateConvertButton();
    }

    updateConvertButton() {
        const convertBtn = document.getElementById('convert-btn');
        const hasFile = this.currentFile !== null;
        const hasFormats = document.getElementById('input-format').value && 
                          document.getElementById('output-format').value;
        
        convertBtn.disabled = !(hasFile && hasFormats);
    }

    toggleAdvancedOptions() {
        const toggleBtn = document.getElementById('toggle-advanced');
        const optionsPanel = document.getElementById('options-panel');
        
        if (optionsPanel.style.display === 'none' || !optionsPanel.style.display) {
            optionsPanel.style.display = 'block';
            toggleBtn.classList.add('active');
        } else {
            optionsPanel.style.display = 'none';
            toggleBtn.classList.remove('active');
        }
    }

    async convertFile() {
        if (!this.currentFile) return;
        
        try {
            this.showLoading(true);
            this.showProgress(true);
            
            // Upload file first
            const uploadResult = await this.uploadFile();
            this.currentFileId = uploadResult.file_id;
            
            // Get conversion settings
            const inputFormat = document.getElementById('input-format').value;
            const outputFormat = document.getElementById('output-format').value;
            const options = this.getConversionOptions();
            
            // Convert file
            const convertResult = await this.performConversion(
                this.currentFileId, 
                inputFormat, 
                outputFormat, 
                options
            );
            
            this.showResult(convertResult);
            await this.loadHistory();
            
        } catch (error) {
            this.showToast(error.message || '转换失败', 'error');
            this.showResult({ status: 'error', message: error.message });
        } finally {
            this.showLoading(false);
            this.showProgress(false);
        }
    }

    async uploadFile() {
        const formData = new FormData();
        formData.append('file', this.currentFile);
        
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'Upload failed');
        }
        
        return await response.json();
    }

    async performConversion(fileId, inputFormat, outputFormat, options) {
        const formData = new FormData();
        formData.append('file_id', fileId);
        formData.append('input_format', inputFormat);
        formData.append('output_format', outputFormat);
        formData.append('options', JSON.stringify(options));
        
        const response = await fetch('/api/convert', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'Conversion failed');
        }
        
        return await response.json();
    }

    getConversionOptions() {
        const options = {};
        
        // Get checkbox options
        const checkboxes = document.querySelectorAll('#options-panel input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                options[checkbox.id.replace('-', '_')] = true;
            }
        });
        
        return options;
    }

    showProgress(show) {
        const progressSection = document.getElementById('progress-section');
        progressSection.style.display = show ? 'block' : 'none';
        
        if (show) {
            // Animate progress bar
            const progressFill = document.getElementById('progress-fill');
            let width = 0;
            const interval = setInterval(() => {
                width += Math.random() * 10;
                if (width >= 90) {
                    clearInterval(interval);
                    width = 90;
                }
                progressFill.style.width = width + '%';
            }, 200);
        }
    }

    showResult(result) {
        const resultSection = document.getElementById('result-section');
        const resultContent = document.getElementById('result-content');
        
        if (result.status === 'success') {
            resultContent.innerHTML = `
                <div class="result-success">
                    <i class="fas fa-check-circle" style="font-size: 2rem; color: var(--success-color); margin-bottom: 1rem;"></i>
                    <h3>转换成功！</h3>
                    <p>${result.message}</p>
                    <button class="btn-download" onclick="window.open('${result.download_url}', '_blank')">
                        <i class="fas fa-download"></i>
                        下载文件
                    </button>
                </div>
            `;
            this.showToast('文件转换成功！', 'success');
        } else {
            resultContent.innerHTML = `
                <div class="result-error">
                    <i class="fas fa-exclamation-circle" style="font-size: 2rem; color: var(--error-color); margin-bottom: 1rem;"></i>
                    <h3>转换失败</h3>
                    <p>${result.message}</p>
                </div>
            `;
            this.showToast('文件转换失败', 'error');
        }
        
        resultSection.style.display = 'block';
    }

    async loadHistory() {
        try {
            const response = await fetch('/api/history');
            const history = await response.json();
            this.renderHistory(history);
        } catch (error) {
            console.error('Failed to load history:', error);
        }
    }

    renderHistory(history) {
        const historyList = document.getElementById('history-list');
        
        if (history.length === 0) {
            historyList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-clock"></i>
                    <p>暂无转换记录</p>
                </div>
            `;
            return;
        }
        
        historyList.innerHTML = history.map(item => `
            <div class="history-item">
                <div class="history-info">
                    <div class="history-filename">${item.filename}</div>
                    <div class="history-meta">
                        ${this.formatDisplayName(item.input_format)} → ${this.formatDisplayName(item.output_format)} | 
                        ${new Date(item.timestamp).toLocaleString()} | 
                        <span class="status-${item.status}">${item.status === 'success' ? '成功' : '失败'}</span>
                    </div>
                </div>
                <div class="history-actions">
                    ${item.status === 'success' && item.output_filename ? 
                        `<button class="btn-history" onclick="window.open('/api/download/${item.output_filename}', '_blank')" title="下载">
                            <i class="fas fa-download"></i>
                        </button>` : ''
                    }
                    <button class="btn-history" onclick="pandocUI.deleteHistoryItem('${item.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    async deleteHistoryItem(fileId) {
        try {
            const response = await fetch(`/api/history/${fileId}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                await this.loadHistory();
                this.showToast('记录已删除', 'success');
            }
        } catch (error) {
            this.showToast('删除失败', 'error');
        }
    }

    async clearHistory() {
        if (!confirm('确定要清空所有转换记录吗？')) return;
        
        try {
            const response = await fetch('/api/history', {
                method: 'DELETE'
            });
            
            if (response.ok) {
                await this.loadHistory();
                this.showToast('历史记录已清空', 'success');
            }
        } catch (error) {
            this.showToast('清空失败', 'error');
        }
    }

    showLoading(show) {
        const loadingOverlay = document.getElementById('loading-overlay');
        loadingOverlay.style.display = show ? 'flex' : 'none';
    }

    showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const icon = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        }[type] || 'fas fa-info-circle';
        
        toast.innerHTML = `
            <i class="${icon}"></i>
            <span>${message}</span>
        `;
        
        toastContainer.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }
}

// Initialize the application
const pandocUI = new PandocWebUI();
