# Pandoc Web UI - 更新日志

## v1.1.2 - 2025-01-16

### 🎨 界面样式优化

#### ✨ 下拉框样式改进
- **优雅设计**: 渐变背景、圆角边框、阴影效果
- **自定义箭头**: SVG 图标替代浏览器默认箭头
- **交互动效**: 悬停、焦点、激活状态的平滑过渡
- **主题统一**: 与整体设计风格保持一致

#### 🎯 格式选择区域优化
- **视觉层次**: 增强的背景渐变和边框设计
- **标签美化**: 添加装饰性圆点和更好的排版
- **转换箭头**: 动画效果和脉冲指示器
- **响应式适配**: 移动设备上的优化显示

#### 🔧 高级选项改进
- **按钮样式**: 渐变背景和光泽效果
- **选项面板**: 统一的卡片式设计
- **复选框优化**: 现代化的复选框样式和动画
- **交互反馈**: 悬停和选中状态的视觉反馈

#### 📱 响应式设计
- **移动适配**: 针对小屏幕设备的样式调整
- **触摸友好**: 更大的点击区域和合适的间距
- **字体缩放**: 适应不同屏幕尺寸的字体大小

---

## v1.1.1 - 2025-01-16

### 🎨 用户体验改进

#### ✨ 新增功能
- **格式扩展名显示**: 在格式选择器中显示文件扩展名，如 "Word Document (.docx)"
- **历史记录优化**: 转换历史中也显示格式的文件扩展名
- **用户友好**: 帮助用户更好地识别和选择正确的文件格式

#### 🔧 技术改进
- **显示名称映射**: 完善的格式名称到扩展名的映射关系
- **一致性体验**: 确保所有界面元素都显示扩展名信息

---

## v1.1.0 - 2025-01-16

### 🎉 重大更新：完整格式支持

#### ✨ 新增功能
- **完整格式支持**: 基于 Pandoc Guide 文档，新增支持 44 种输入格式和 50+ 种输出格式
- **智能格式检测**: 增强的文件扩展名自动检测，支持更多文件类型
- **格式显示优化**: 为所有格式提供友好的中文显示名称

#### 📄 新增输入格式
- **学术格式**: BibTeX, BibLaTeX, JATS XML, BITS XML, EndNote XML, CSL JSON, RIS
- **Wiki 格式**: DokuWiki, TikiWiki, TWiki, Vimwiki
- **标记语言**: Creole, Djot, txt2tags, Muse, Haddock
- **数据格式**: CSV, TSV, JSON, Native Haskell
- **专业格式**: Jupyter Notebook, Perl POD, Manual Pages, OPML, Typst
- **电子书**: FictionBook2
- **Markdown 变体**: CommonMark, CommonMark Extended, MultiMarkdown, PHP Markdown Extra, Strict Markdown, GitHub Flavored Markdown

#### 📄 新增输出格式
- **演示文稿**: Reveal.js, Slidy, Slideous, DZSlides, S5, PowerPoint PPTX
- **学术出版**: JATS XML 变体, TEI Simple, DocBook 4/5
- **排版系统**: ConTeXt, Typst, GNU Texinfo
- **设计软件**: InDesign ICML
- **Wiki 输出**: XWiki, ZimWiki
- **轻量标记**: AsciiDoc, AsciiDoc Legacy, Markua
- **文档系统**: Manual Pages, Roff Ms
- **特殊格式**: ANSI Text, Chunked HTML, OpenDocument XML

#### 🔧 技术改进
- **扩展名映射**: 完善的文件扩展名到格式的映射关系
- **输出文件名**: 智能的输出文件扩展名生成
- **格式验证**: 确保输入输出格式的有效性检查
- **错误处理**: 改进的格式转换错误提示

---

## v1.0.0 - 2025-01-16

### 🎉 首次发布

#### ✨ 核心功能
- **文件上传**: 支持拖拽上传，最大 50MB 文件
- **格式转换**: 基于 Pandoc 的多格式文档转换
- **实时预览**: 转换进度实时显示
- **历史管理**: 转换历史记录和文件管理
- **响应式设计**: 完美适配桌面和移动设备

#### 🎨 用户界面
- **现代设计**: 渐变色主题，优雅的视觉效果
- **流畅动画**: 丰富的交互动效和过渡效果
- **直观操作**: 简洁明了的用户操作流程
- **主题统一**: 一致的色彩和样式规范

#### 🛠 技术栈
- **后端**: FastAPI + Python 3.8+
- **前端**: HTML5 + CSS3 + JavaScript ES6+
- **转换引擎**: Pandoc 2.0+
- **UI 框架**: 响应式 CSS Grid/Flexbox

---

## 技术规格

### 系统要求
- Python 3.8+
- Pandoc 2.0+
- 现代浏览器支持

### 性能特性
- 异步文件处理
- 自动文件清理
- 内存优化管理
- 并发请求支持

### 安全特性
- 文件大小限制
- 格式验证检查
- 临时文件管理
- 错误处理机制

---

**感谢使用 Pandoc Web UI！** 🎉
