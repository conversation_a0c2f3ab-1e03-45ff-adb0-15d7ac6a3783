# Pandoc Web UI - 更新日志

## v1.1.0 - 2025-01-16

### 🎉 重大更新：完整格式支持

#### ✨ 新增功能
- **完整格式支持**: 基于 Pandoc Guide 文档，新增支持 44 种输入格式和 50+ 种输出格式
- **智能格式检测**: 增强的文件扩展名自动检测，支持更多文件类型
- **格式显示优化**: 为所有格式提供友好的中文显示名称

#### 📄 新增输入格式
- **学术格式**: BibTeX, BibLaTeX, JATS XML, BITS XML, EndNote XML, CSL JSON, RIS
- **Wiki 格式**: DokuWiki, TikiWiki, TWiki, Vimwiki
- **标记语言**: Creole, Djot, txt2tags, Muse, Haddock
- **数据格式**: CSV, TSV, JSON, Native Haskell
- **专业格式**: Jupyter Notebook, Perl POD, Manual Pages, OPML, Typst
- **电子书**: FictionBook2
- **Markdown 变体**: CommonMark, CommonMark Extended, MultiMarkdown, PHP Markdown Extra, Strict Markdown, GitHub Flavored Markdown

#### 📄 新增输出格式
- **演示文稿**: Reveal.js, Slidy, Slideous, DZSlides, S5, PowerPoint PPTX
- **学术出版**: JATS XML 变体, TEI Simple, DocBook 4/5
- **排版系统**: ConTeXt, Typst, GNU Texinfo
- **设计软件**: InDesign ICML
- **Wiki 输出**: XWiki, ZimWiki
- **轻量标记**: AsciiDoc, AsciiDoc Legacy, Markua
- **文档系统**: Manual Pages, Roff Ms
- **特殊格式**: ANSI Text, Chunked HTML, OpenDocument XML

#### 🔧 技术改进
- **扩展名映射**: 完善的文件扩展名到格式的映射关系
- **输出文件名**: 智能的输出文件扩展名生成
- **格式验证**: 确保输入输出格式的有效性检查
- **错误处理**: 改进的格式转换错误提示

#### 📚 文档更新
- **README**: 完整的格式支持列表和说明
- **测试文件**: 新增格式测试文档 `test_formats.md`
- **更新日志**: 详细的版本更新记录

---

## v1.0.0 - 2025-01-16

### 🎉 首次发布

#### ✨ 核心功能
- **文件上传**: 支持拖拽上传，最大 50MB 文件
- **格式转换**: 基于 Pandoc 的多格式文档转换
- **实时预览**: 转换进度实时显示
- **历史管理**: 转换历史记录和文件管理
- **响应式设计**: 完美适配桌面和移动设备

#### 🎨 用户界面
- **现代设计**: 渐变色主题，优雅的视觉效果
- **流畅动画**: 丰富的交互动效和过渡效果
- **直观操作**: 简洁明了的用户操作流程
- **主题统一**: 一致的色彩和样式规范

#### 🛠 技术栈
- **后端**: FastAPI + Python 3.8+
- **前端**: HTML5 + CSS3 + JavaScript ES6+
- **转换引擎**: Pandoc 2.0+
- **UI 框架**: 响应式 CSS Grid/Flexbox

#### 📦 项目结构
- **模块化设计**: 清晰的代码组织结构
- **配置管理**: 完善的项目配置和依赖管理
- **文档完整**: 详细的使用说明和 API 文档

#### 🚀 部署支持
- **跨平台**: Windows, macOS, Linux 支持
- **简易启动**: 一键启动脚本
- **Docker 就绪**: 容器化部署支持

---

## 技术规格

### 系统要求
- Python 3.8+
- Pandoc 2.0+
- 现代浏览器支持

### 性能特性
- 异步文件处理
- 自动文件清理
- 内存优化管理
- 并发请求支持

### 安全特性
- 文件大小限制
- 格式验证检查
- 临时文件管理
- 错误处理机制

---

## 未来计划

### v1.2.0 计划
- [ ] 批量文件转换
- [ ] 自定义转换模板
- [ ] 云存储集成
- [ ] 用户认证系统

### v1.3.0 计划
- [ ] 实时协作编辑
- [ ] 版本控制集成
- [ ] 插件系统
- [ ] 高级配置选项

---

## 贡献指南

欢迎提交 Issue 和 Pull Request！

### 开发环境
```bash
git clone <repository>
cd PandocWebUI
pip install -r requirements.txt
python main.py
```

### 代码规范
- 遵循 PEP 8 Python 代码规范
- 使用 ESLint 进行 JavaScript 代码检查
- 保持代码注释的完整性

---

**感谢使用 Pandoc Web UI！** 🎉
