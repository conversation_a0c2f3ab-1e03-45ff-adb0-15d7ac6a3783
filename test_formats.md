# Pandoc Web UI 格式支持测试

这是一个用于测试 Pandoc Web UI 新增格式支持的综合测试文档。

## 支持的输入格式

### 文档格式
- **Markdown 变体**: Pandoc Markdown, CommonMark, GitHub Flavored Markdown, MultiMarkdown, PHP Markdown Extra, Strict Markdown
- **Office 文档**: Word DOCX, OpenDocument Text (ODT), Rich Text Format (RTF)
- **网页格式**: HTML, XHTML
- **学术格式**: LaTeX, reStructuredText, DocBook

### 标记语言
- **Wiki 格式**: MediaWiki, DokuWiki, TikiWiki, TWiki, XWiki, ZimWiki, Vimwiki
- **其他标记**: Textile, Creole, Muse, Djot, txt2tags
- **文档系统**: Emacs Org Mode, Haddock, Typst

### 数据格式
- **表格数据**: CSV, TSV
- **结构化数据**: JSON, YAML
- **参考文献**: BibTeX, BibLaTeX, CSL JSON, RIS, EndNote XML

### 专业格式
- **电子书**: EPUB, FictionBook2
- **学术出版**: JATS XML, BITS XML
- **笔记本**: Jupyter Notebook (ipynb)
- **文档**: Perl POD, Manual Pages (man, mdoc)

## 支持的输出格式

### 网页和演示
- **HTML 变体**: HTML5, HTML4, XHTML, Chunked HTML
- **幻灯片**: Reveal.js, Slidy, Slideous, DZSlides, S5, LaTeX Beamer
- **Office 演示**: PowerPoint (PPTX)

### 文档格式
- **Office**: Word DOCX, OpenDocument Text, PowerPoint PPTX
- **PDF**: 便携式文档格式
- **电子书**: EPUB v2/v3, FictionBook2
- **学术**: LaTeX, ConTeXt, DocBook 4/5

### 标记语言输出
- **Markdown 变体**: 各种 Markdown 方言
- **Wiki 格式**: 各种 Wiki 标记语言
- **轻量标记**: AsciiDoc, Textile, reStructuredText

### 专业和技术格式
- **排版系统**: LaTeX, ConTeXt, Typst
- **文档系统**: GNU Texinfo, Manual Pages
- **设计软件**: InDesign ICML
- **学术出版**: JATS XML 变体, TEI Simple

## 测试内容

### 基本文本格式
这里有**粗体**、*斜体*、`代码`和~~删除线~~文本。

### 列表
1. 有序列表项 1
2. 有序列表项 2
   - 嵌套无序列表
   - 另一个嵌套项

### 代码块
```python
def test_conversion():
    """测试文档转换功能"""
    formats = ['html', 'docx', 'pdf', 'epub']
    for fmt in formats:
        print(f"Converting to {fmt}")
    return "Success"
```

### 表格
| 输入格式 | 输出格式 | 状态 |
|----------|----------|------|
| Markdown | HTML | ✅ |
| DOCX | PDF | ✅ |
| LaTeX | EPUB | ✅ |

### 数学公式
行内数学：$E = mc^2$

块级数学：
$$
\sum_{i=1}^{n} x_i = \frac{n(n+1)}{2}
$$

### 引用
> 这是一个引用块，用于测试引用格式的转换。
> 
> 支持多行引用内容。

### 链接和图片
- 链接：[Pandoc 官网](https://pandoc.org)
- 图片：![Pandoc Logo](https://pandoc.org/diagram.jpg)

### 脚注
这是一个带脚注的句子[^1]。

[^1]: 这是脚注内容。

## 转换建议

### 常用转换组合
1. **Markdown → HTML**: 网页发布
2. **Markdown → PDF**: 文档分享
3. **DOCX → Markdown**: 版本控制
4. **LaTeX → DOCX**: 协作编辑
5. **HTML → EPUB**: 电子书制作

### 格式特性
- **HTML**: 支持样式和交互
- **PDF**: 固定布局，适合打印
- **EPUB**: 响应式电子书格式
- **DOCX**: 兼容 Microsoft Office
- **LaTeX**: 高质量学术排版

## 测试结论

通过这个测试文档，可以验证 Pandoc Web UI 对各种格式的支持情况，确保转换功能的完整性和准确性。
