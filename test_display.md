# 格式显示测试

这是一个用于测试格式显示功能的文档。

## 测试内容

### 基本格式测试
- **粗体文本**
- *斜体文本*
- `行内代码`

### 列表测试
1. 第一项
2. 第二项
   - 子项目 A
   - 子项目 B

### 代码块测试
```javascript
function testFormatDisplay() {
    console.log("测试格式显示功能");
    return "成功";
}
```

### 表格测试
| 格式 | 扩展名 | 描述 |
|------|--------|------|
| Markdown | .md | 轻量级标记语言 |
| HTML | .html | 网页标记语言 |
| PDF | .pdf | 便携式文档格式 |

现在前端应该显示：
- 输入格式：Pandoc Markdown (.md)
- 输出格式：HTML5 (.html)、PDF (.pdf) 等

这样用户可以清楚地看到每种格式对应的文件扩展名。
