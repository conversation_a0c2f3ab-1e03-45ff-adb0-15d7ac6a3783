# Pandoc Web UI 测试文档

这是一个用于测试 Pandoc Web UI 转换功能的示例文档。

## 功能特性

### 文本格式

这里有一些**粗体文本**和*斜体文本*，还有`行内代码`。

### 列表

#### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

#### 有序列表
1. 第一步
2. 第二步
3. 第三步

### 代码块

```python
def hello_world():
    print("Hello, Pandoc Web UI!")
    return "转换成功"

# 调用函数
result = hello_world()
```

### 表格

| 功能 | 状态 | 描述 |
|------|------|------|
| 文件上传 | ✅ | 支持拖拽上传 |
| 格式转换 | ✅ | 多种格式支持 |
| 历史记录 | ✅ | 转换历史管理 |

### 引用

> 这是一个引用块。
> 
> Pandoc 是一个强大的文档转换工具，支持多种格式之间的转换。

### 链接

访问 [Pandoc 官网](https://pandoc.org) 了解更多信息。

## 数学公式

行内公式：$E = mc^2$

块级公式：
$$
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
$$

## 结论

这个测试文档包含了常见的 Markdown 元素，可以用来验证 Pandoc Web UI 的转换功能是否正常工作。
