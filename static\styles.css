/* Pandoc Web UI - Modern CSS Styles */

/* CSS Variables for Theme */
:root {
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Main Content */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 140px);
}

/* Cards */
.card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-secondary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-header i {
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
}

/* Upload Area */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    transform: scale(1.02);
}

.upload-content {
    position: relative;
    z-index: 2;
}

.upload-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.upload-text {
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.upload-hint {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* File Info */
.file-info {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.file-details {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.file-details i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.file-meta {
    flex: 1;
}

.file-name {
    display: block;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.file-size {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.btn-remove {
    background: none;
    border: none;
    color: var(--error-color);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.btn-remove:hover {
    background: rgba(239, 68, 68, 0.1);
}

/* Format Selection */
.format-selection {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: end;
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.03), rgba(118, 75, 162, 0.03));
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.format-selection::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.format-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.format-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.format-group label::before {
    content: '';
    width: 0.25rem;
    height: 0.25rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
}

.format-select {
    padding: 0.875rem 2.5rem 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    position: relative;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23667eea' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    box-shadow: var(--shadow-sm);
}

.format-select:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.format-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15), var(--shadow-lg);
    transform: translateY(-1px);
}

.format-select:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Custom dropdown options styling */
.format-select option {
    padding: 0.75rem 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-weight: 500;
    border: none;
}

.format-select option:hover,
.format-select option:focus {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.format-select option:checked {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.format-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-top: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    animation: pulse 2s infinite;
}

.format-arrow::before {
    content: '';
    position: absolute;
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 50%;
    z-index: -1;
    animation: ripple 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes ripple {
    0% { transform: scale(0.8); opacity: 1; }
    100% { transform: scale(1.2); opacity: 0; }
}

/* Advanced Options */
.advanced-options {
    margin-bottom: 2.5rem;
}

.btn-toggle {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    color: var(--primary-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    padding: 0.875rem 1.25rem;
    transition: all 0.3s ease;
    width: 100%;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.btn-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-toggle:hover {
    color: var(--primary-dark);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-toggle:hover::before {
    left: 100%;
}

.btn-toggle i {
    transition: transform 0.3s ease;
    font-size: 1.1rem;
}

.btn-toggle.active i {
    transform: rotate(180deg);
}

.options-panel {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: var(--radius-lg);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    position: relative;
}

.options-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.option-group {
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
    transition: all 0.2s ease;
}

.option-group:last-child {
    margin-bottom: 0;
}

.option-group:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: var(--primary-color);
    transform: translateX(0.25rem);
}

.option-group label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-primary);
    position: relative;
}

.option-group input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    accent-color: var(--primary-color);
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 0.25rem;
}

.option-group input[type="checkbox"]:checked {
    transform: scale(1.1);
}

.option-group input[type="checkbox"]:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* Buttons */
.btn-convert {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-convert:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-convert:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-clear {
    background: none;
    border: 1px solid var(--error-color);
    color: var(--error-color);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.btn-clear:hover {
    background: var(--error-color);
    color: white;
}

/* Progress */
.progress-content {
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 0.5rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-sm);
    transition: width 0.3s ease;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: 200px 0; }
}

.progress-text {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Result */
.result-success {
    padding: 1.5rem;
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: var(--radius-lg);
    text-align: center;
}

.result-error {
    padding: 1.5rem;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: var(--radius-lg);
    text-align: center;
}

.btn-download {
    background: var(--success-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    margin-top: 1rem;
}

.btn-download:hover {
    background: #059669;
    transform: translateY(-1px);
}

/* History */
.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.history-item:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

.history-info {
    flex: 1;
}

.history-filename {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.history-meta {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.history-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-history {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-history:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-light);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-light);
    padding: 1rem 0;
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.footer a {
    color: var(--primary-color);
    text-decoration: none;
}

.footer a:hover {
    text-decoration: underline;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1100;
}

.toast {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 1rem 1.5rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }
    
    .format-selection {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 1rem;
    }

    .format-arrow {
        transform: rotate(90deg);
        margin: 0;
        font-size: 1.25rem;
    }

    .format-arrow::before {
        width: 2.5rem;
        height: 2.5rem;
    }

    .format-group label {
        font-size: 0.8rem;
    }

    .format-select {
        padding: 0.75rem 2rem 0.75rem 0.875rem;
        font-size: 0.9rem;
        background-size: 0.875rem;
        background-position: right 0.625rem center;
    }

    .btn-toggle {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .options-panel {
        padding: 1rem;
    }

    .option-group {
        padding: 0.625rem 0.875rem;
    }

    .option-group label {
        font-size: 0.9rem;
        gap: 0.625rem;
    }

    .option-group input[type="checkbox"] {
        width: 1.125rem;
        height: 1.125rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .card-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .history-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .history-actions {
        align-self: stretch;
        justify-content: flex-end;
    }
    
    .toast {
        min-width: auto;
        max-width: calc(100vw - 2rem);
    }
}
