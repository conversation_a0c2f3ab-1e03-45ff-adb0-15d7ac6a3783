# Pandoc Web UI

一个基于 FastAPI 和现代 Web 技术构建的 Pandoc 文档转换工具的可视化界面。

## 功能特性

### 🎨 优美的用户界面

- 现代化的响应式设计
- 渐变色主题和流畅动画
- 直观的拖拽上传体验
- 移动端友好的布局

### 📄 强大的文档转换

- 支持 Pandoc 的所有主要格式
- 输入格式：Markdown, HTML, DOCX, ODT, EPUB, LaTeX, RST, Textile 等
- 输出格式：HTML, PDF, DOCX, ODT, EPUB, LaTeX, Markdown 等
- 自动格式检测
- 高级转换选项配置

### 📊 转换历史管理

- 实时转换历史记录
- 一键下载转换结果
- 批量清理历史记录
- 转换状态跟踪

### ⚡ 高性能体验

- 异步文件处理
- 实时进度反馈
- 自动文件清理
- 错误处理和重试机制

## 系统要求

### 必需软件

- Python 3.8+
- Pandoc 2.0+
- 现代浏览器（Chrome, Firefox, Safari, Edge）

### 推荐配置

- 内存：2GB+
- 存储：1GB+ 可用空间
- 网络：用于下载依赖包

## 安装指南

### 1. 安装 Pandoc

#### Windows

```bash
# 使用 Chocolatey
choco install pandoc

# 或下载安装包
# https://github.com/jgm/pandoc/releases
```

#### macOS

```bash
# 使用 Homebrew
brew install pandoc

# 或使用 MacPorts
sudo port install pandoc
```

#### Linux (Ubuntu/Debian)

```bash
sudo apt update
sudo apt install pandoc

# 或安装最新版本
wget https://github.com/jgm/pandoc/releases/download/3.1.9/pandoc-3.1.9-1-amd64.deb
sudo dpkg -i pandoc-3.1.9-1-amd64.deb
```

### 2. 安装 Python 依赖

```bash
# 克隆项目
git clone <repository-url>
cd PandocWebUI

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 启动应用

```bash
# 开发模式
python main.py

# 或使用 uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### 4. 访问应用

打开浏览器访问：http://localhost:8000

## 使用说明

### 基本转换流程

1. **上传文件**

   - 点击上传区域选择文件
   - 或直接拖拽文件到上传区域
   - 支持最大 50MB 的文件
2. **选择格式**

   - 输入格式：可选择自动检测或手动指定
   - 输出格式：从支持的格式列表中选择
   - 系统会根据文件扩展名自动推荐格式
3. **配置选项**（可选）

   - 点击"高级选项"展开更多设置
   - 生成独立文档：包含完整的 HTML 头部
   - 包含目录：自动生成文档目录
   - 章节编号：为标题添加编号
4. **开始转换**

   - 点击"开始转换"按钮
   - 查看实时转换进度
   - 转换完成后自动显示结果
5. **下载结果**

   - 转换成功后点击"下载文件"
   - 文件会保存到浏览器默认下载目录

### 历史记录管理

- **查看历史**：所有转换记录都会显示在历史区域
- **重新下载**：点击下载图标重新下载之前的转换结果
- **删除记录**：点击垃圾桶图标删除单个记录
- **清空历史**：点击"清空历史"按钮删除所有记录

## 支持的格式

### 输入格式（44种）

#### 文档格式

- **Markdown 变体**: Pandoc Markdown (.md), CommonMark, GitHub Flavored Markdown, MultiMarkdown, PHP Markdown Extra, Strict Markdown
- **Office 文档**: Word DOCX (.docx), OpenDocument Text (.odt), Rich Text Format (.rtf)
- **网页格式**: HTML (.html, .htm), XHTML
- **学术格式**: LaTeX (.tex), reStructuredText (.rst), DocBook

#### 标记语言

- **Wiki 格式**: MediaWiki, DokuWiki, TikiWiki, TWiki, Vimwiki
- **其他标记**: Textile, Creole (.creole), Muse, Djot (.dj), txt2tags (.t2t)
- **文档系统**: Emacs Org Mode (.org), Haddock, Typst (.typ)

#### 数据格式

- **表格数据**: CSV (.csv), TSV (.tsv)
- **结构化数据**: JSON (.json), Native Haskell
- **参考文献**: BibTeX (.bib), BibLaTeX, CSL JSON, RIS (.ris), EndNote XML

#### 专业格式

- **电子书**: EPUB (.epub), FictionBook2 (.fb2)
- **学术出版**: JATS XML, BITS XML
- **笔记本**: Jupyter Notebook (.ipynb)
- **文档**: Perl POD (.pod), Manual Pages (.man), OPML (.opml)

### 输出格式（50+种）

#### 网页和演示

- **HTML 变体**: HTML5 (.html), HTML4, XHTML, Chunked HTML (.zip)
- **幻灯片**: Reveal.js (.html), Slidy (.html), Slideous (.html), DZSlides (.html), S5 (.html), LaTeX Beamer (.tex)
- **Office 演示**: PowerPoint PPTX (.pptx)

#### 文档格式

- **Office**: Word DOCX (.docx), OpenDocument Text (.odt), OpenDocument XML (.xml)
- **PDF**: 便携式文档格式 (.pdf)
- **电子书**: EPUB v2/v3 (.epub), FictionBook2 (.fb2)
- **学术**: LaTeX (.tex), ConTeXt (.tex), DocBook 4/5 (.xml)

#### 标记语言输出

- **Markdown 变体**: 各种 Markdown 方言 (.md)
- **Wiki 格式**: MediaWiki (.wiki), DokuWiki, XWiki, ZimWiki 等
- **轻量标记**: AsciiDoc (.adoc), Textile, reStructuredText (.rst)

#### 专业和技术格式

- **排版系统**: LaTeX (.tex), ConTeXt (.tex), Typst (.typ)
- **文档系统**: GNU Texinfo (.texi), Manual Pages (.1), Roff Ms (.ms)
- **设计软件**: InDesign ICML (.icml)
- **学术出版**: JATS XML 变体 (.xml), TEI Simple (.xml)
- **数据格式**: JSON (.json), CSV (.csv), TSV (.tsv)
- **其他**: Plain Text (.txt), ANSI Text (.txt), Native Haskell (.hs)

## 高级配置

### PDF 转换配置

对于 PDF 输出，需要安装 LaTeX 引擎：

```bash
# Windows (MiKTeX)
# 下载并安装 MiKTeX: https://miktex.org/

# macOS (MacTeX)
brew install --cask mactex

# Linux (TeX Live)
sudo apt install texlive-full
```

### 自定义样式

可以通过修改 `static/styles.css` 来自定义界面样式：

```css
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
    /* 更多自定义变量 */
}
```

## API 文档

应用提供了完整的 REST API，启动后访问：

- API 文档：http://localhost:8000/docs
- 交互式文档：http://localhost:8000/redoc

### 主要 API 端点

- `GET /api/info` - 获取应用信息
- `GET /api/formats` - 获取支持的格式列表
- `POST /api/upload` - 上传文件
- `POST /api/convert` - 转换文档
- `GET /api/download/{filename}` - 下载文件
- `GET /api/history` - 获取转换历史
- `DELETE /api/history/{id}` - 删除历史记录

## 故障排除

### 常见问题

1. **Pandoc 未找到**

   ```
   解决方案：确保 Pandoc 已正确安装并添加到系统 PATH
   验证：在终端运行 `pandoc --version`
   ```
2. **PDF 转换失败**

   ```
   解决方案：安装 LaTeX 引擎（如 MiKTeX, MacTeX, TeX Live）
   验证：在终端运行 `pdflatex --version`
   ```
3. **文件上传失败**

   ```
   解决方案：检查文件大小是否超过 50MB 限制
   检查文件格式是否受支持
   ```
4. **端口占用**

   ```
   解决方案：修改启动命令中的端口号
   uvicorn main:app --port 8001
   ```

### 日志查看

应用日志会显示在终端中，包含详细的错误信息和调试信息。

## 开发指南

### 项目结构

```
PandocWebUI/
├── main.py              # FastAPI 应用主文件
├── requirements.txt     # Python 依赖
├── README.md           # 项目说明
├── static/             # 静态文件目录
│   ├── index.html      # 主页面
│   ├── styles.css      # 样式文件
│   └── script.js       # JavaScript 逻辑
├── uploads/            # 上传文件目录（自动创建）
└── outputs/            # 输出文件目录（自动创建）
```

### 开发环境设置

```bash
# 安装开发依赖
pip install -r requirements.txt

# 启动开发服务器
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 代码贡献

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 致谢

- [Pandoc](https://pandoc.org/) - 强大的文档转换工具
- [FastAPI](https://fastapi.tiangolo.com/) - 现代 Python Web 框架
- [Font Awesome](https://fontawesome.com/) - 图标库
- [Google Fonts](https://fonts.google.com/) - 字体服务

## 联系方式如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件
- 创建 Discussion

---

**享受使用 Pandoc Web UI！** 🚀
